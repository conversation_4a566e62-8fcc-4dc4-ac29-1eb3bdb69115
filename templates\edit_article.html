{% extends "base.html" %}

{% block title %}編輯文章 - {{ article.title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit me-2"></i>編輯文章</h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('article_detail', id=article.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-eye me-1"></i>查看文章
                </a>
                <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>

        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-edit me-2"></i>文章內容</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="title" class="form-label">標題 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="{{ article.title }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="summary" class="form-label">摘要</label>
                                <textarea class="form-control" id="summary" name="summary" rows="3" 
                                          placeholder="簡短描述文章內容...">{{ article.summary or '' }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="content" class="form-label">內容 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="content" name="content" rows="15" required 
                                          placeholder="在這裡輸入文章內容...">{{ article.content }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-cog me-2"></i>文章設定</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="category" class="form-label">分類</label>
                                <input type="text" class="form-control" id="category" name="category" 
                                       value="{{ article.category or '' }}" placeholder="例：技術、生活、隨筆">
                            </div>

                            <div class="mb-3">
                                <label for="tags" class="form-label">標籤</label>
                                <input type="text" class="form-control" id="tags" name="tags" 
                                       value="{{ article.tags or '' }}" placeholder="用逗號分隔，例：Python, Flask, Web開發">
                                <small class="form-text text-muted">多個標籤請用逗號分隔</small>
                            </div>

                            <div class="mb-3">
                                <label for="cover_image" class="form-label">封面圖片</label>
                                {% if article.cover_image %}
                                    <div class="mb-2">
                                        <img src="{{ url_for('static', filename='uploads/' + article.cover_image) }}" 
                                             class="img-thumbnail" style="max-width: 100%; height: 150px; object-fit: cover;" 
                                             alt="當前封面">
                                        <small class="d-block text-muted mt-1">當前封面圖片</small>
                                    </div>
                                {% endif %}
                                <input type="file" class="form-control" id="cover_image" name="cover_image" 
                                       accept="image/*">
                                <small class="form-text text-muted">
                                    支援 PNG, JPG, JPEG, GIF, WebP 格式
                                    {% if article.cover_image %}（上傳新圖片將替換當前封面）{% endif %}
                                </small>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>更新文章
                                </button>
                                <a href="{{ url_for('article_detail', id=article.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 文章資訊 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6><i class="fas fa-info me-2"></i>文章資訊</h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">
                                <div class="mb-2">
                                    <strong>創建時間：</strong><br>
                                    {{ article.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                                <div class="mb-2">
                                    <strong>最後更新：</strong><br>
                                    {{ article.updated_at.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                                <div>
                                    <strong>當前字數：</strong>
                                    <span id="word-count">{{ article.content|length }}</span> 字
                                </div>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 字數統計
document.addEventListener('DOMContentLoaded', function() {
    const contentTextarea = document.getElementById('content');
    const wordCountSpan = document.getElementById('word-count');
    
    function updateWordCount() {
        wordCountSpan.textContent = contentTextarea.value.length;
    }
    
    contentTextarea.addEventListener('input', updateWordCount);
});
</script>
{% endblock %}