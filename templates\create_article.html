{% extends "base.html" %}

{% block title %}新增文章 - 個人文章管理系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus me-2"></i>新增文章</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
        </div>

        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-edit me-2"></i>文章內容</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="title" class="form-label">標題 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>

                            <div class="mb-3">
                                <label for="summary" class="form-label">摘要</label>
                                <textarea class="form-control" id="summary" name="summary" rows="3" 
                                          placeholder="簡短描述文章內容..."></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="content" class="form-label">內容 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="content" name="content" rows="15" required 
                                          placeholder="在這裡輸入文章內容..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-cog me-2"></i>文章設定</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="category" class="form-label">分類</label>
                                <input type="text" class="form-control" id="category" name="category" 
                                       placeholder="例：技術、生活、隨筆">
                            </div>

                            <div class="mb-3">
                                <label for="tags" class="form-label">標籤</label>
                                <input type="text" class="form-control" id="tags" name="tags" 
                                       placeholder="用逗號分隔，例：Python, Flask, Web開發">
                                <small class="form-text text-muted">多個標籤請用逗號分隔</small>
                            </div>

                            <div class="mb-3">
                                <label for="cover_image" class="form-label">封面圖片</label>
                                <input type="file" class="form-control" id="cover_image" name="cover_image" 
                                       accept="image/*">
                                <small class="form-text text-muted">支援 PNG, JPG, JPEG, GIF, WebP 格式</small>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>發布文章
                                </button>
                                <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 預覽區域 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6><i class="fas fa-eye me-2"></i>即時預覽</h6>
                        </div>
                        <div class="card-body">
                            <div id="preview-title" class="fw-bold mb-2 text-muted">標題預覽</div>
                            <div id="preview-summary" class="small text-muted mb-2">摘要預覽</div>
                            <div id="preview-category" class="mb-2"></div>
                            <div id="preview-tags"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 即時預覽功能
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const summaryInput = document.getElementById('summary');
    const categoryInput = document.getElementById('category');
    const tagsInput = document.getElementById('tags');
    
    const previewTitle = document.getElementById('preview-title');
    const previewSummary = document.getElementById('preview-summary');
    const previewCategory = document.getElementById('preview-category');
    const previewTags = document.getElementById('preview-tags');
    
    function updatePreview() {
        previewTitle.textContent = titleInput.value || '標題預覽';
        previewSummary.textContent = summaryInput.value || '摘要預覽';
        
        if (categoryInput.value) {
            previewCategory.innerHTML = `<span class="badge bg-primary">${categoryInput.value}</span>`;
        } else {
            previewCategory.innerHTML = '';
        }
        
        if (tagsInput.value) {
            const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);
            previewTags.innerHTML = tags.map(tag => `<span class="badge bg-secondary me-1">#${tag}</span>`).join('');
        } else {
            previewTags.innerHTML = '';
        }
    }
    
    titleInput.addEventListener('input', updatePreview);
    summaryInput.addEventListener('input', updatePreview);
    categoryInput.addEventListener('input', updatePreview);
    tagsInput.addEventListener('input', updatePreview);
});
</script>
{% endblock %}