{% extends "base.html" %}

{% block title %}首頁 - 個人文章管理系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-newspaper me-2"></i>我的文章</h1>
            <a href="{{ url_for('create_article') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>新增文章
            </a>
        </div>

        <!-- 搜尋和篩選 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">搜尋文章</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ current_search }}" placeholder="輸入標題或內容關鍵字">
                    </div>
                    <div class="col-md-4">
                        <label for="category" class="form-label">分類篩選</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">所有分類</option>
                            {% for cat in categories %}
                                <option value="{{ cat }}" {% if cat == current_category %}selected{% endif %}>
                                    {{ cat }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search me-1"></i>搜尋
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 文章列表 -->
        {% if articles.items %}
            <div class="row">
                {% for article in articles.items %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card article-card h-100">
                            {% if article.cover_image %}
                                <img src="{{ url_for('static', filename='uploads/' + article.cover_image) }}" 
                                     class="card-img-top cover-image" alt="{{ article.title }}">
                            {% else %}
                                <div class="card-img-top cover-image bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                            
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">{{ article.title }}</h5>
                                
                                {% if article.summary %}
                                    <p class="card-text text-muted">{{ article.summary[:100] }}{% if article.summary|length > 100 %}...{% endif %}</p>
                                {% endif %}
                                
                                <div class="mt-auto">
                                    {% if article.category %}
                                        <span class="badge bg-secondary mb-2">{{ article.category }}</span>
                                    {% endif %}
                                    
                                    {% if article.tags %}
                                        <div class="mb-2">
                                            {% for tag in article.tags.split(',') %}
                                                <span class="badge bg-light text-dark me-1">#{{ tag.strip() }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    
                                    <small class="text-muted d-block mb-2">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ article.updated_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                    
                                    <div class="btn-group w-100" role="group">
                                        <a href="{{ url_for('article_detail', id=article.id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>查看
                                        </a>
                                        <a href="{{ url_for('edit_article', id=article.id) }}" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-edit me-1"></i>編輯
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- 分頁 -->
            {% if articles.pages > 1 %}
                <nav aria-label="文章分頁">
                    <ul class="pagination justify-content-center">
                        {% if articles.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('index', page=articles.prev_num, search=current_search, category=current_category) }}">
                                    上一頁
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in articles.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != articles.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('index', page=page_num, search=current_search, category=current_category) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if articles.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('index', page=articles.next_num, search=current_search, category=current_category) }}">
                                    下一頁
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-5x text-muted mb-3"></i>
                <h3 class="text-muted">還沒有文章</h3>
                <p class="text-muted">開始創建你的第一篇文章吧！</p>
                <a href="{{ url_for('create_article') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>新增文章
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}