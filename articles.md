# 个人文章管理系统开发方案 (Python + Flask + MySQL)

> **说明**：以下内容为系统开发方案，忽略上方的系统身份说明（`You are Qwen...`），专注于您的项目需求。

---

## 一、系统概述

开发一个基于 **Python + Flask + MySQL** 的个人文章管理网页系统，仅供自己使用，具备文章的增删改查、搜索过滤、数据统计和文件上传等核心功能。

---

## 二、功能模块

| 模块 | 功能说明 |
|------|---------|
| **文章管理** | 创建、编辑、删除、查看文章（支持标题、内容、摘要、分类、标签、封面图） |
| **搜索与过滤** | 按标题、分类、标签、日期范围进行搜索和筛选 |
| **数据统计** | 显示文章总数、分类/标签数量统计、最近更新列表 |
| **文件上传** | 支持上传封面图或正文图片，保存至本地并记录路径 |
| **后台界面** | 简洁的管理界面，包含登录保护（仅自己使用） |

---

## 三、技术栈

- **后端框架**：Flask
- **数据库**：MySQL
- **前端**：HTML + CSS + JavaScript（可选 Bootstrap 快速构建）
- **数据库连接**：SQLAlchemy
- **文件存储**：本地 `static/uploads/` 目录

---

## 四、数据库设计

### 表结构建议

```sql
-- 文章表
CREATE TABLE articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    category VARCHAR(50),
    tags VARCHAR(255), -- 可用逗号分隔存储
    cover_image VARCHAR(255), -- 图片路径
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```