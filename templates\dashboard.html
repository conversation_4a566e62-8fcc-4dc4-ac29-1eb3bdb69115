{% extends "base.html" %}

{% block title %}統計儀表板 - 個人文章管理系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1><i class="fas fa-chart-bar me-2"></i>統計儀表板</h1>
        <p class="text-muted mb-4">查看你的文章統計數據和最近動態</p>
    </div>
</div>

<div class="row" id="stats-container">
    <div class="col-md-12 text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">載入中...</span>
        </div>
        <p class="mt-2 text-muted">正在載入統計數據...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            renderStats(data);
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('stats-container').innerHTML = `
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>載入統計數據時發生錯誤
                    </div>
                </div>
            `;
        });
});

function renderStats(data) {
    const container = document.getElementById('stats-container');
    
    let html = `
        <!-- 總覽統計 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <h2 class="display-4">${data.total_articles}</h2>
                        <p class="mb-0">總文章數</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white text-center">
                    <div class="card-body">
                        <i class="fas fa-folder fa-3x mb-3"></i>
                        <h2 class="display-4">${data.category_stats.length}</h2>
                        <p class="mb-0">分類數量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-3x mb-3"></i>
                        <h2 class="display-4">${data.recent_articles.length}</h2>
                        <p class="mb-0">最近文章</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 分類統計 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>分類統計</h5>
                    </div>
                    <div class="card-body">
    `;
    
    if (data.category_stats.length > 0) {
        html += '<div class="list-group list-group-flush">';
        data.category_stats.forEach(stat => {
            const percentage = ((stat.count / data.total_articles) * 100).toFixed(1);
            html += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-primary me-2">${stat.category}</span>
                    </div>
                    <div>
                        <span class="badge bg-secondary">${stat.count} 篇 (${percentage}%)</span>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    } else {
        html += '<p class="text-muted text-center">暫無分類數據</p>';
    }
    
    html += `
                    </div>
                </div>
            </div>

            <!-- 最近文章 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>最近更新</h5>
                    </div>
                    <div class="card-body">
    `;
    
    if (data.recent_articles.length > 0) {
        html += '<div class="list-group list-group-flush">';
        data.recent_articles.forEach(article => {
            const updateDate = new Date(article.updated_at).toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            html += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">
                            <a href="/article/${article.id}" class="text-decoration-none">
                                ${article.title}
                            </a>
                        </h6>
                        <small class="text-muted">${updateDate}</small>
                    </div>
                    ${article.summary ? `<p class="mb-1 text-muted small">${article.summary.substring(0, 80)}${article.summary.length > 80 ? '...' : ''}</p>` : ''}
                    ${article.category ? `<small class="badge bg-light text-dark">${article.category}</small>` : ''}
                </div>
            `;
        });
        html += '</div>';
    } else {
        html += '<p class="text-muted text-center">暫無文章</p>';
    }
    
    html += `
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt me-2"></i>快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="/create" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-plus me-2"></i>新增文章
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-list me-2"></i>查看所有文章
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100 mb-2" onclick="location.reload()">
                                    <i class="fas fa-sync me-2"></i>重新整理數據
                                </button>
                            </div>
                            <div class="col-md-3">
                                <a href="/?search=" class="btn btn-outline-secondary w-100 mb-2">
                                    <i class="fas fa-search me-2"></i>搜尋文章
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}
</script>
{% endblock %}