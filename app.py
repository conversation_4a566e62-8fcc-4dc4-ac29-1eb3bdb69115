from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.utils import secure_filename
from datetime import datetime, timezone
import os
import secrets
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'mysql+pymysql://root:password@localhost/article_system')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 確保上傳目錄存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

db = SQLAlchemy(app)

# 文章模型
class Article(db.Model):
    __tablename__ = 'articles'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    summary = db.Column(db.Text)
    category = db.Column(db.String(50))
    tags = db.Column(db.String(255))
    cover_image = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    def __repr__(self):
        return f'<Article {self.title}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'summary': self.summary,
            'category': self.category,
            'tags': self.tags,
            'cover_image': self.cover_image,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# 允許的檔案類型
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

def allowed_file(filename):
    if not filename or '.' not in filename:
        return False
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in ALLOWED_EXTENSIONS

def validate_file(file):
    """驗證上傳的檔案"""
    if not file or not file.filename:
        return False, "未選擇檔案"

    if not allowed_file(file.filename):
        return False, f"不支援的檔案格式。支援格式：{', '.join(ALLOWED_EXTENSIONS)}"

    # 檢查檔案大小（如果可能的話）
    file.seek(0, 2)  # 移到檔案末尾
    file_size = file.tell()
    file.seek(0)  # 重置檔案指標

    if file_size > MAX_FILE_SIZE:
        return False, f"檔案大小超過限制（最大 {MAX_FILE_SIZE // (1024*1024)}MB）"

    return True, "檔案驗證通過"

def generate_csrf_token():
    """生成 CSRF token"""
    if '_csrf_token' not in session:
        session['_csrf_token'] = secrets.token_hex(16)
    return session['_csrf_token']

def validate_csrf_token(token):
    """驗證 CSRF token"""
    return token and session.get('_csrf_token') == token

@app.context_processor
def inject_csrf_token():
    """將 CSRF token 注入到模板上下文"""
    return dict(csrf_token=generate_csrf_token)

# 首頁 - 文章列表
@app.route('/')
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '').strip()
    category = request.args.get('category', '').strip()

    # 驗證頁碼
    if page < 1:
        page = 1

    try:
        query = Article.query

        if search:
            query = query.filter(Article.title.contains(search) | Article.content.contains(search))

        if category:
            query = query.filter(Article.category == category)

        articles = query.order_by(Article.updated_at.desc()).paginate(
            page=page, per_page=10, error_out=False
        )

        # 如果頁碼超出範圍，重定向到最後一頁
        if page > articles.pages and articles.pages > 0:
            return redirect(url_for('index', page=articles.pages, search=search, category=category))

        # 獲取所有分類用於篩選
        categories = db.session.query(Article.category).distinct().filter(Article.category.isnot(None)).all()
        categories = [cat[0] for cat in categories if cat[0]]

        return render_template('index.html', articles=articles, categories=categories,
                             current_search=search, current_category=category)
    except Exception as e:
        flash(f'載入文章列表時發生錯誤：{str(e)}', 'error')
        # 返回空的分頁結果
        from flask_sqlalchemy import Pagination
        empty_articles = Pagination(query=None, page=1, per_page=10, total=0, items=[])
        return render_template('index.html', articles=empty_articles, categories=[],
                             current_search=search, current_category=category)

# 文章詳情
@app.route('/article/<int:id>')
def article_detail(id):
    article = Article.query.get_or_404(id)
    return render_template('article_detail.html', article=article)

# 新增文章
@app.route('/create', methods=['GET', 'POST'])
def create_article():
    if request.method == 'POST':
        # 驗證 CSRF token
        csrf_token = request.form.get('csrf_token')
        if not validate_csrf_token(csrf_token):
            flash('安全驗證失敗，請重新提交！', 'error')
            return render_template('create_article.html')

        # 驗證必填欄位
        title = request.form.get('title', '').strip()
        content = request.form.get('content', '').strip()
        summary = request.form.get('summary', '').strip()
        category = request.form.get('category', '').strip()
        tags = request.form.get('tags', '').strip()

        # 檢查必填欄位
        if not title:
            flash('標題為必填欄位！', 'error')
            return render_template('create_article.html')

        if not content:
            flash('內容為必填欄位！', 'error')
            return render_template('create_article.html')
        
        # 處理封面圖片上傳
        cover_image = None
        if 'cover_image' in request.files:
            file = request.files['cover_image']
            if file and file.filename:
                is_valid, message = validate_file(file)
                if is_valid:
                    try:
                        filename = secure_filename(file.filename)
                        # 添加時間戳避免檔名衝突
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                        filename = timestamp + filename

                        # 確保檔案路徑安全
                        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                        if not file_path.startswith(os.path.abspath(app.config['UPLOAD_FOLDER'])):
                            flash('檔案路徑不安全！', 'error')
                            return render_template('create_article.html')

                        file.save(file_path)
                        cover_image = filename
                    except Exception as e:
                        flash(f'檔案上傳失敗：{str(e)}', 'error')
                        return render_template('create_article.html')
                else:
                    flash(f'檔案驗證失敗：{message}', 'error')
                    return render_template('create_article.html')
        
        try:
            article = Article(
                title=title,
                content=content,
                summary=summary,
                category=category,
                tags=tags,
                cover_image=cover_image
            )

            db.session.add(article)
            db.session.commit()

            flash('文章創建成功！', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            db.session.rollback()
            flash(f'創建文章時發生錯誤：{str(e)}', 'error')
            return render_template('create_article.html')
    
    return render_template('create_article.html')

# 編輯文章
@app.route('/edit/<int:id>', methods=['GET', 'POST'])
def edit_article(id):
    article = Article.query.get_or_404(id)
    
    if request.method == 'POST':
        # 驗證 CSRF token
        csrf_token = request.form.get('csrf_token')
        if not validate_csrf_token(csrf_token):
            flash('安全驗證失敗，請重新提交！', 'error')
            return render_template('edit_article.html', article=article)

        # 驗證必填欄位
        title = request.form.get('title', '').strip()
        content = request.form.get('content', '').strip()
        summary = request.form.get('summary', '').strip()
        category = request.form.get('category', '').strip()
        tags = request.form.get('tags', '').strip()

        # 檢查必填欄位
        if not title:
            flash('標題為必填欄位！', 'error')
            return render_template('edit_article.html', article=article)

        if not content:
            flash('內容為必填欄位！', 'error')
            return render_template('edit_article.html', article=article)

        article.title = title
        article.content = content
        article.summary = summary
        article.category = category
        article.tags = tags
        
        # 處理封面圖片更新
        if 'cover_image' in request.files:
            file = request.files['cover_image']
            if file and file.filename:
                is_valid, message = validate_file(file)
                if is_valid:
                    try:
                        # 刪除舊圖片
                        if article.cover_image:
                            old_file_path = os.path.join(app.config['UPLOAD_FOLDER'], article.cover_image)
                            if os.path.exists(old_file_path):
                                os.remove(old_file_path)

                        filename = secure_filename(file.filename)
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                        filename = timestamp + filename

                        # 確保檔案路徑安全
                        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                        if not file_path.startswith(os.path.abspath(app.config['UPLOAD_FOLDER'])):
                            flash('檔案路徑不安全！', 'error')
                            return render_template('edit_article.html', article=article)

                        file.save(file_path)
                        article.cover_image = filename
                    except Exception as e:
                        flash(f'檔案上傳失敗：{str(e)}', 'error')
                        return render_template('edit_article.html', article=article)
                else:
                    flash(f'檔案驗證失敗：{message}', 'error')
                    return render_template('edit_article.html', article=article)
        
        try:
            article.updated_at = datetime.now(timezone.utc)
            db.session.commit()

            flash('文章更新成功！', 'success')
            return redirect(url_for('article_detail', id=article.id))
        except Exception as e:
            db.session.rollback()
            flash(f'更新文章時發生錯誤：{str(e)}', 'error')
            return render_template('edit_article.html', article=article)
    
    return render_template('edit_article.html', article=article)

# 刪除文章
@app.route('/delete/<int:id>', methods=['POST'])
def delete_article(id):
    # 驗證 CSRF token
    csrf_token = request.form.get('csrf_token')
    if not validate_csrf_token(csrf_token):
        flash('安全驗證失敗，請重新提交！', 'error')
        return redirect(url_for('index'))

    article = Article.query.get_or_404(id)
    
    # 刪除關聯的圖片檔案
    if article.cover_image:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], article.cover_image)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    try:
        db.session.delete(article)
        db.session.commit()

        flash('文章刪除成功！', 'success')
        return redirect(url_for('index'))
    except Exception as e:
        db.session.rollback()
        flash(f'刪除文章時發生錯誤：{str(e)}', 'error')
        return redirect(url_for('index'))

# 統計數據 API
@app.route('/api/stats')
def get_stats():
    try:
        total_articles = Article.query.count()

        # 分類統計
        category_stats = db.session.query(
            Article.category,
            db.func.count(Article.id)
        ).filter(Article.category.isnot(None)).group_by(Article.category).all()

        # 最近更新的文章
        recent_articles = Article.query.order_by(Article.updated_at.desc()).limit(5).all()

        return jsonify({
            'total_articles': total_articles,
            'category_stats': [{'category': cat, 'count': count} for cat, count in category_stats],
            'recent_articles': [article.to_dict() for article in recent_articles]
        })
    except Exception as e:
        return jsonify({
            'error': f'載入統計數據時發生錯誤：{str(e)}',
            'total_articles': 0,
            'category_stats': [],
            'recent_articles': []
        }), 500

# 儀表板
@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

# 添加錯誤處理器
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('500.html'), 500

# 添加請求後清理
@app.teardown_appcontext
def shutdown_session(exception=None):
    if hasattr(db.session, 'remove'):
        db.session.remove()

if __name__ == '__main__':
    try:
        with app.app_context():
            db.create_all()
        app.run(debug=True)
    except Exception as e:
        print(f"應用程式啟動失敗：{str(e)}")
        exit(1)