from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.utils import secure_filename
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'mysql+pymysql://root:password@localhost/article_system')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 確保上傳目錄存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

db = SQLAlchemy(app)

# 文章模型
class Article(db.Model):
    __tablename__ = 'articles'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    summary = db.Column(db.Text)
    category = db.Column(db.String(50))
    tags = db.Column(db.String(255))
    cover_image = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Article {self.title}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'summary': self.summary,
            'category': self.category,
            'tags': self.tags,
            'cover_image': self.cover_image,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# 允許的檔案類型
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# 首頁 - 文章列表
@app.route('/')
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    
    query = Article.query
    
    if search:
        query = query.filter(Article.title.contains(search) | Article.content.contains(search))
    
    if category:
        query = query.filter(Article.category == category)
    
    articles = query.order_by(Article.updated_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # 獲取所有分類用於篩選
    categories = db.session.query(Article.category).distinct().filter(Article.category.isnot(None)).all()
    categories = [cat[0] for cat in categories if cat[0]]
    
    return render_template('index.html', articles=articles, categories=categories, 
                         current_search=search, current_category=category)

# 文章詳情
@app.route('/article/<int:id>')
def article_detail(id):
    article = Article.query.get_or_404(id)
    return render_template('article_detail.html', article=article)

# 新增文章
@app.route('/create', methods=['GET', 'POST'])
def create_article():
    if request.method == 'POST':
        title = request.form['title']
        content = request.form['content']
        summary = request.form['summary']
        category = request.form['category']
        tags = request.form['tags']
        
        # 處理封面圖片上傳
        cover_image = None
        if 'cover_image' in request.files:
            file = request.files['cover_image']
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # 添加時間戳避免檔名衝突
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
                cover_image = filename
        
        article = Article(
            title=title,
            content=content,
            summary=summary,
            category=category,
            tags=tags,
            cover_image=cover_image
        )
        
        db.session.add(article)
        db.session.commit()
        
        flash('文章創建成功！', 'success')
        return redirect(url_for('index'))
    
    return render_template('create_article.html')

# 編輯文章
@app.route('/edit/<int:id>', methods=['GET', 'POST'])
def edit_article(id):
    article = Article.query.get_or_404(id)
    
    if request.method == 'POST':
        article.title = request.form['title']
        article.content = request.form['content']
        article.summary = request.form['summary']
        article.category = request.form['category']
        article.tags = request.form['tags']
        
        # 處理封面圖片更新
        if 'cover_image' in request.files:
            file = request.files['cover_image']
            if file and file.filename and allowed_file(file.filename):
                # 刪除舊圖片
                if article.cover_image:
                    old_file_path = os.path.join(app.config['UPLOAD_FOLDER'], article.cover_image)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)
                
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
                article.cover_image = filename
        
        article.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash('文章更新成功！', 'success')
        return redirect(url_for('article_detail', id=article.id))
    
    return render_template('edit_article.html', article=article)

# 刪除文章
@app.route('/delete/<int:id>', methods=['POST'])
def delete_article(id):
    article = Article.query.get_or_404(id)
    
    # 刪除關聯的圖片檔案
    if article.cover_image:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], article.cover_image)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    db.session.delete(article)
    db.session.commit()
    
    flash('文章刪除成功！', 'success')
    return redirect(url_for('index'))

# 統計數據 API
@app.route('/api/stats')
def get_stats():
    total_articles = Article.query.count()
    
    # 分類統計
    category_stats = db.session.query(
        Article.category, 
        db.func.count(Article.id)
    ).filter(Article.category.isnot(None)).group_by(Article.category).all()
    
    # 最近更新的文章
    recent_articles = Article.query.order_by(Article.updated_at.desc()).limit(5).all()
    
    return jsonify({
        'total_articles': total_articles,
        'category_stats': [{'category': cat, 'count': count} for cat, count in category_stats],
        'recent_articles': [article.to_dict() for article in recent_articles]
    })

# 儀表板
@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)