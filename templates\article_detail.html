{% extends "base.html" %}

{% block title %}{{ article.title }} - 個人文章管理系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-start mb-4">
            <div>
                <h1>{{ article.title }}</h1>
                <div class="text-muted mb-3">
                    <i class="fas fa-calendar me-2"></i>創建於 {{ article.created_at.strftime('%Y-%m-%d %H:%M') }}
                    {% if article.updated_at != article.created_at %}
                        <span class="ms-3">
                            <i class="fas fa-edit me-1"></i>更新於 {{ article.updated_at.strftime('%Y-%m-%d %H:%M') }}
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="btn-group" role="group">
                <a href="{{ url_for('edit_article', id=article.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>編輯
                </a>
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    <i class="fas fa-trash me-1"></i>刪除
                </button>
            </div>
        </div>

        {% if article.cover_image %}
            <div class="mb-4">
                <img src="{{ url_for('static', filename='uploads/' + article.cover_image) }}" 
                     class="img-fluid rounded" alt="{{ article.title }}" style="max-height: 400px; width: 100%; object-fit: cover;">
            </div>
        {% endif %}

        <div class="row">
            <div class="col-md-8">
                {% if article.summary %}
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>摘要</h5>
                        <p class="mb-0">{{ article.summary }}</p>
                    </div>
                {% endif %}

                <div class="card">
                    <div class="card-body">
                        <div class="article-content">
                            {{ article.content|safe|replace('\n', '<br>')|replace('\r', '') }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info me-2"></i>文章資訊</h5>
                    </div>
                    <div class="card-body">
                        {% if article.category %}
                            <div class="mb-3">
                                <strong>分類：</strong>
                                <span class="badge bg-primary">{{ article.category }}</span>
                            </div>
                        {% endif %}

                        {% if article.tags %}
                            <div class="mb-3">
                                <strong>標籤：</strong><br>
                                {% for tag in article.tags.split(',') %}
                                    <span class="badge bg-secondary me-1 mb-1">#{{ tag.strip() }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mb-3">
                            <strong>字數：</strong>
                            <span class="text-muted">{{ article.content|length }} 字</span>
                        </div>

                        <div class="mb-3">
                            <strong>創建時間：</strong><br>
                            <small class="text-muted">{{ article.created_at.strftime('%Y年%m月%d日 %H:%M') }}</small>
                        </div>

                        {% if article.updated_at != article.created_at %}
                            <div class="mb-3">
                                <strong>最後更新：</strong><br>
                                <small class="text-muted">{{ article.updated_at.strftime('%Y年%m月%d日 %H:%M') }}</small>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 刪除確認模態框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">確認刪除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>確定要刪除文章「<strong>{{ article.title }}</strong>」嗎？</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>此操作無法復原！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form method="POST" action="{{ url_for('delete_article', id=article.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">確認刪除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.article-content {
    line-height: 1.8;
    font-size: 1.1rem;
}
</style>
{% endblock %}